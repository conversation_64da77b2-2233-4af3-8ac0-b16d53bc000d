spring.application.name=backend
spring.datasource.url=****************************************************************************************!
spring.datasource.driverClassName=org.postgresql.Driver
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
spring.jackson.serialization.fail-on-empty-beans=false
spring.datasource.hikari.maximum-pool-size=3

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=

spring.mail.protocol=smtp
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

#minio.url=https://play.min.io
#minio.access.key=TuyeAz0gmG1GR8VlIWQj
#minio.secret.key=VrWtAoZ54MmQ6lrdwD0necH2xxCaQCmbiEcQTKwx

minio.url=https://lapxpert-storage-api.khoalda.dev
minio.access.key=lznAoWtYPW5ddXW7mwZ6
minio.secret.key=xToXrilK2a6rC70EDyyanIFTiv8avJPnLjf7ZDp0

spring.data.redis.host=lapxpert-redis.khoalda.dev
spring.data.redis.port=6379
spring.data.redis.database=0
spring.data.redis.timeout=2000ms
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0
spring.data.redis.lettuce.pool.max-wait=-1ms
spring.cache.type=redis

logging.level.org.springframework.cache=DEBUG
logging.level.org.springframework.security=DEBUG

# File Upload Configuration
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.servlet.multipart.enabled=true

spring.liquibase.change-log=classpath:/db/changelog/db.changelog-master.xml

# VNPay Configuration
vnpay.tmn-code=${VNPAY_TMN_CODE:4FWARVVC}
vnpay.hash-secret=${VNPAY_HASH_SECRET:7UG6NK3YS9C59FYCM1F7UHOT8H2INKAP}
vnpay.pay-url=${VNPAY_PAY_URL:https://sandbox.vnpayment.vn/paymentv2/vpcpay.html}
vnpay.return-url=${VNPAY_RETURN_URL:/api/payment/vnpay-payment}
vnpay.api-url=${VNPAY_API_URL:https://sandbox.vnpayment.vn/merchant_webapi/api/transaction}