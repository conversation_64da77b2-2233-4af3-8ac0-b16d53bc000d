# LapXpert - Functional Requirements

LapXpert is a backend application for an e-commerce website. It provides API services for both admin (management) and client (user) interfaces, supporting full-featured e-commerce operations.

---

## 1. Tech Stack

- **Language**: Java 21  
- **Framework**: Spring Boot  
- **Database**: PostgreSQL  
- **ORM**: JPA (Hibernate)  
- **Object Mapping**: MapStruct  
- **Boilerplate Reduction**: Lombok  
- **Authentication**: JWT (JSON Web Token)  
- **Authorization**: Spring Security  
- **Caching**: Redis Cache  

---

## 2. Core Modules

The application includes the following functional modules:

- Product  
- User  
- Order  
- Payment  
- Invoice  
- Voucher  
- Category  
- Brand  
- Review  
- Cart  
- Wishlist  
- Promotion  
- Statistics  

Each module must implement full business logic, database schema handling, and integration with the existing codebase.

---

## 3. Admin (Management) Interface Requirements

### 3.1 CRUD Operations
- Provide full Create, Read, Update, and Delete operations for each module.

### 3.2 Filtering
- Support field-specific filters per module.
  - *Example*: Product filters by name, price, category, brand, etc.

### 3.3 Pagination
- All list APIs should support pagination.

### 3.4 Search
- Provide keyword-based search functionality per module.

### 3.5 Excel Import/Export
- Import data from Excel files.
- Export data to Excel format.

### 3.6 Role-Based Access Control (RBAC)
- Define and enforce access permissions per user role for each module.

### 3.7 Audit Logging
- Record all significant user and system actions with timestamp and user info.

### 3.8 Statistics
- Display key metrics and statistical data per module (e.g., revenue, sales volume, user count).

---

## 4. Client (User) Interface Requirements

### 4.1 Product Browsing
- Filter products by name, price, category, brand, etc.

### 4.2 Cart and Wishlist
- Add/remove/view items in shopping cart.
- Add/remove/view items in wishlist.

### 4.3 Order History
- View detailed order history with status and timestamps.

### 4.4 User Profile
- View and update personal profile and account settings.

---

## 5. Business Logic and Data Management

### 5.1 Business Rules
- Enforce domain-specific rules for each module, such as:
  - Voucher application logic
  - Inventory validation
  - Order lifecycle management
  - Promotion conditions
  - Review posting constraints

### 5.2 Database Integration
- Implement and manage database schema for all modules.
- Handle existing data appropriately and ensure backward compatibility.

### 5.3 Codebase Integration
- Integrate new features into the existing codebase.
- Ensure modular structure and maintainable architecture.

---
