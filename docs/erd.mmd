erDiagram
    %% User Management Domain
    NguoiDung {
        Long id PK
        String maNguoiDung UK
        String avatar
        String hoTen
        GioiTinh gioiTinh
        LocalDate ngaySinh
        String cccd UK
        String email UK
        String soDienThoai UK
        String matKhau
        VaiTro vaiTro
        Boolean trangThai
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    DiaChi {
        Long id PK
        Long nguoiDungId FK
        String duong
        String phuongXa
        String quanHuyen
        String tinhThanh
        String quocGia
        String loaiDiaChi
        Boolean laMacDinh
        ZonedDateTime ngayTao
        ZonedDateTime ngayCapNhat
    }
    
    %% Product Management Domain
    SanPham {
        Long id PK
        String maSanPham UK
        String tenSanPham
        Long thuongHieuId FK
        String moTa
        List hinhAnh
        LocalDate ngayRaMat
        Boolean trangThai
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    SanPhamChiTiet {
        Long id PK
        Long sanPhamId FK
        String sku UK
        String mauSac
        BigDecimal giaBan
        BigDecimal giaKhuyenMai
        List hinhAnh
        Boolean trangThai
        Instant ngayTao
        Instant ngayCapNhat
        Long cpuId FK
        Long ramId FK
        Long gpuId FK
        Long manHinhId FK
        Long oCungId FK
        Long pinId FK
        Long heDieuHanhId FK
        Long amThanhId FK
        Long banPhimId FK
        Long baoMatId FK
        Long congGiaoTiepId FK
        Long ketNoiMangId FK
        Long thietKeId FK
        Long webcamId FK
    }
    
    %% Order Management Domain
    HoaDon {
        Long id PK
        String maHoaDon UK
        Long khachHangId FK
        Long nhanVienId FK
        String diaChiGiaoHangHoTen
        String diaChiGiaoHangSoDienThoai
        String diaChiGiaoHangDuong
        String diaChiGiaoHangPhuongXa
        String diaChiGiaoHangQuanHuyen
        String diaChiGiaoHangTinhThanh
        BigDecimal tongTienHang
        BigDecimal giaTriGiamGiaVoucher
        BigDecimal phiVanChuyen
        BigDecimal tongThanhToan
        TrangThaiDonHang trangThaiDonHang
        TrangThaiThanhToan trangThaiThanhToan
        LoaiHoaDon loaiHoaDon
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    HoaDonChiTiet {
        Long id PK
        Long hoaDonId FK
        Long sanPhamChiTietId FK
        Integer soLuong
        BigDecimal giaGoc
        BigDecimal giaBan
        BigDecimal thanhTien
        String tenSanPhamSnapshot
        String skuSnapshot
        String hinhAnhSnapshot
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    LichSuHoaDon {
        Long id PK
        Long hoaDonId FK
        Long nguoiThucHienId FK
        TrangThaiDonHang trangThai
        String mieuTa
        Instant thoiGian
    }
    
    ThanhToan {
        Long id PK
        Long nguoiDungId FK
        String maGiaoDich
        BigDecimal giaTri
        String ghiChu
        Instant thoiGianThanhToan
        TrangThaiGiaoDich trangThaiGiaoDich
        PhuongThucThanhToan phuongThucThanhToan
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    %% Voucher Management Domain
    PhieuGiamGia {
        Long id PK
        String maPhieuGiamGia UK
        Boolean loaiPhieuGiamGia
        TrangThaiPhieuGiamGia trangThai
        BigDecimal giaTriGiam
        BigDecimal giaTriDonHangToiThieu
        Instant ngayBatDau
        Instant ngayKetThuc
        String moTa
        Boolean phieuRiengTu
        Integer soLuongBanDau
        Integer soLuongDaDung
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    PhieuGiamGiaNguoiDung {
        Long phieuGiamGiaId FK
        Long nguoiDungId FK
        OffsetDateTime ngayNhan
        Boolean daSuDung
    }
    
    HoaDonPhieuGiamGia {
        Long hoaDonId FK
        Long phieuGiamGiaId FK
        BigDecimal giaTriDaGiam
    }
    
    %% Discount Campaign Domain
    DotGiamGia {
        Long id PK
        String maDotGiamGia UK
        String tenDotGiamGia
        BigDecimal phanTramGiam
        Instant ngayBatDau
        Instant ngayKetThuc
        Boolean daAn
        TrangThai trangThai
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    %% Product Attributes
    ThuongHieu {
        Long id PK
        String moTaThuongHieu
    }
    
    DanhMuc {
        Long id PK
        String maDanhMuc UK
        String tenDanhMuc
        Instant ngayTao
        Instant ngayCapNhat
    }
    
    Cpu {
        Long id PK
        String moTaCpu
    }
    
    Ram {
        Long id PK
        String moTaRam
    }
    
    %% Relationships
    NguoiDung ||--o{ DiaChi : "has addresses"
    NguoiDung ||--o{ HoaDon : "places orders (customer)"
    NguoiDung ||--o{ HoaDon : "processes orders (staff)"
    NguoiDung ||--o{ LichSuHoaDon : "performs actions"
    NguoiDung ||--o{ ThanhToan : "makes payments"
    NguoiDung ||--o{ PhieuGiamGiaNguoiDung : "assigned vouchers"
    
    SanPham ||--|| ThuongHieu : "belongs to brand"
    SanPham ||--o{ SanPhamChiTiet : "has variants"
    SanPham }|--|| DanhMuc : "categorized in"
    
    SanPhamChiTiet ||--|| Cpu : "has processor"
    SanPhamChiTiet ||--|| Ram : "has memory"
    SanPhamChiTiet ||--o{ HoaDonChiTiet : "ordered in"
    SanPhamChiTiet }|--|| DotGiamGia : "discounted by"
    
    HoaDon ||--o{ HoaDonChiTiet : "contains items"
    HoaDon ||--o{ LichSuHoaDon : "has history"
    HoaDon ||--o{ HoaDonPhieuGiamGia : "uses vouchers"
    
    PhieuGiamGia ||--o{ PhieuGiamGiaNguoiDung : "assigned to users"
    PhieuGiamGia ||--o{ HoaDonPhieuGiamGia : "applied to orders"